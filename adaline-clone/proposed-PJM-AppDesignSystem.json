{"designSystem": {"name": "PJM Africa Experience", "version": "2.0.0", "extractedColors": {"primary": {"purple": "#5B21B6", "purpleText": "#553C9A", "purpleLight": "#8B5CF6", "purpleDark": "#4C1D95"}, "backgrounds": {"pageBackground": "#F8FAFC", "cardBackground": "#FFFFFF", "heroOverlay": "rgba(0, 0, 0, 0.6)", "sidebarBackground": "#FFFFFF"}, "text": {"primary": "#1E293B", "secondary": "#64748B", "tertiary": "#94A3B8", "inverse": "#FFFFFF", "link": "#5B21B6", "accent": "#F97316"}, "borders": {"light": "#E2E8F0", "medium": "#CBD5E1", "focus": "#5B21B6"}, "status": {"success": "#10B981", "warning": "#F59E0B", "error": "#EF4444", "info": "#3B82F6"}}}, "elementStyling": {"navigation": {"topBar": {"background": "#FFFFFF", "borderBottom": "1px solid #E2E8F0", "shadow": "0 1px 3px rgba(0, 0, 0, 0.1)", "height": "64px", "padding": "0 24px", "links": {"color": "#64748B", "fontSize": "14px", "fontWeight": "500", "hover": {"color": "#5B21B6"}}, "userActions": {"background": "transparent", "color": "#64748B", "hover": {"background": "#F1F5F9", "borderRadius": "6px"}}}}, "cards": {"heroCard": {"background": "linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.7) 100%)", "backgroundImage": "url(speaker-photo)", "backgroundSize": "cover", "backgroundPosition": "center", "borderRadius": "16px", "padding": "32px", "minHeight": "320px", "position": "relative", "overflow": "hidden", "title": {"color": "#FFFFFF", "fontSize": "32px", "fontWeight": "700", "lineHeight": "1.2", "marginBottom": "8px"}, "subtitle": {"color": "#E2E8F0", "fontSize": "18px", "fontWeight": "500", "marginBottom": "16px"}, "logo": {"position": "absolute", "bottom": "32px", "left": "32px", "filter": "brightness(0) invert(1)"}, "DO_NOT": ["Apply gradient to text elements", "Use card background colors on icons", "Apply image overlay to other elements"]}, "contentCard": {"background": "#FFFFFF", "border": "1px solid #E2E8F0", "borderRadius": "12px", "padding": "24px", "shadow": "0 1px 3px rgba(0, 0, 0, 0.1)", "hover": {"shadow": "0 4px 12px rgba(0, 0, 0, 0.15)", "transform": "translateY(-2px)", "transition": "all 0.2s ease"}, "header": {"marginBottom": "16px", "borderBottom": "1px solid #F1F5F9", "paddingBottom": "16px"}, "DO_NOT": ["Apply hover effects to card content", "Use card shadows on individual text elements"]}, "statusCard": {"background": "#FEF3C7", "border": "1px solid #FCD34D", "borderRadius": "8px", "padding": "12px 16px", "icon": {"color": "#F59E0B", "size": "16px"}, "text": {"color": "#92400E", "fontSize": "14px", "fontWeight": "500"}}}, "buttons": {"primary": {"background": "#5B21B6", "color": "#FFFFFF", "border": "none", "borderRadius": "8px", "padding": "12px 20px", "fontSize": "14px", "fontWeight": "600", "cursor": "pointer", "hover": {"background": "#4C1D95", "transform": "translateY(-1px)", "shadow": "0 4px 12px rgba(91, 33, 182, 0.4)"}, "active": {"background": "#4C1D95", "transform": "translateY(0px)"}, "focus": {"outline": "2px solid #8B5CF6", "outlineOffset": "2px"}, "DO_NOT": ["Apply button background to text", "Use button hover effects on icons"]}, "secondary": {"background": "transparent", "color": "#5B21B6", "border": "1px solid #5B21B6", "borderRadius": "8px", "padding": "12px 20px", "fontSize": "14px", "fontWeight": "600", "hover": {"background": "#F8FAFC", "borderColor": "#4C1D95"}}, "ghost": {"background": "transparent", "color": "#64748B", "border": "none", "borderRadius": "8px", "padding": "8px 12px", "fontSize": "14px", "fontWeight": "500", "hover": {"background": "#F1F5F9", "color": "#1E293B"}}}, "forms": {"toggleSwitch": {"track": {"width": "44px", "height": "24px", "borderRadius": "12px", "background": "#CBD5E1", "active": {"background": "#5B21B6"}, "transition": "background-color 0.2s ease"}, "thumb": {"width": "20px", "height": "20px", "borderRadius": "50%", "background": "#FFFFFF", "shadow": "0 2px 4px rgba(0, 0, 0, 0.2)", "transform": "translateX(2px)", "active": {"transform": "translateX(22px)"}, "transition": "transform 0.2s ease"}, "DO_NOT": ["Apply track colors to thumb", "Use switch shadows on container"]}, "inputField": {"background": "#FFFFFF", "border": "1px solid #E2E8F0", "borderRadius": "8px", "padding": "12px 16px", "fontSize": "14px", "color": "#1E293B", "placeholder": {"color": "#94A3B8"}, "focus": {"borderColor": "#5B21B6", "outline": "none", "shadow": "0 0 0 3px rgba(91, 33, 182, 0.1)"}}}, "avatars": {"small": {"width": "32px", "height": "32px", "borderRadius": "50%", "border": "2px solid #FFFFFF", "shadow": "0 2px 4px rgba(0, 0, 0, 0.1)"}, "medium": {"width": "40px", "height": "40px", "borderRadius": "50%", "border": "2px solid #FFFFFF", "shadow": "0 2px 4px rgba(0, 0, 0, 0.1)"}, "large": {"width": "48px", "height": "48px", "borderRadius": "50%", "border": "2px solid #FFFFFF", "shadow": "0 2px 4px rgba(0, 0, 0, 0.1)"}, "statusIndicator": {"width": "12px", "height": "12px", "borderRadius": "50%", "border": "2px solid #FFFFFF", "position": "absolute", "bottom": "0px", "right": "0px", "online": {"background": "#10B981"}, "offline": {"background": "#94A3B8"}}}, "icons": {"navigation": {"size": "20px", "color": "#64748B", "hover": {"color": "#5B21B6"}}, "action": {"size": "16px", "color": "#64748B", "hover": {"color": "#1E293B"}}, "status": {"size": "16px", "color": "#F59E0B"}, "decorative": {"size": "24px", "color": "#CBD5E1"}, "DO_NOT": ["Apply card backgrounds to icons", "Use button gradients on icon fills", "Apply hover effects to decorative icons"]}, "text": {"headings": {"h1": {"fontSize": "32px", "fontWeight": "700", "lineHeight": "1.2", "color": "#1E293B", "marginBottom": "16px"}, "h2": {"fontSize": "24px", "fontWeight": "600", "lineHeight": "1.3", "color": "#1E293B", "marginBottom": "12px"}, "h3": {"fontSize": "18px", "fontWeight": "600", "lineHeight": "1.4", "color": "#1E293B", "marginBottom": "8px"}}, "body": {"large": {"fontSize": "16px", "fontWeight": "400", "lineHeight": "1.6", "color": "#1E293B"}, "medium": {"fontSize": "14px", "fontWeight": "400", "lineHeight": "1.5", "color": "#64748B"}, "small": {"fontSize": "12px", "fontWeight": "400", "lineHeight": "1.4", "color": "#94A3B8"}}, "links": {"color": "#5B21B6", "textDecoration": "none", "fontWeight": "500", "hover": {"color": "#4C1D95", "textDecoration": "underline"}}}}, "layoutPatterns": {"pageStructure": {"container": {"maxWidth": "1200px", "margin": "0 auto", "padding": "0 24px"}, "twoColumnLayout": {"display": "grid", "gridTemplateColumns": "2fr 1fr", "gap": "32px", "alignItems": "start"}, "spacing": {"sectionGap": "48px", "elementGap": "24px", "componentGap": "16px", "textGap": "8px"}}, "sidebar": {"background": "#FFFFFF", "padding": "24px", "borderRadius": "12px", "border": "1px solid #E2E8F0", "shadow": "0 1px 3px rgba(0, 0, 0, 0.1)", "sections": {"marginBottom": "32px", "lastChild": {"marginBottom": "0"}}}}, "componentStates": {"default": {"transition": "all 0.2s ease"}, "hover": {"cards": {"transform": "translateY(-2px)", "shadow": "0 4px 12px rgba(0, 0, 0, 0.15)"}, "buttons": {"transform": "translateY(-1px)", "shadow": "0 4px 12px rgba(91, 33, 182, 0.4)"}, "links": {"color": "#4C1D95"}}, "focus": {"outline": "2px solid #8B5CF6", "outlineOffset": "2px"}, "active": {"buttons": {"transform": "translateY(0px)"}}}, "shadows": {"card": "0 1px 3px rgba(0, 0, 0, 0.1)", "cardHover": "0 4px 12px rgba(0, 0, 0, 0.15)", "button": "0 4px 12px rgba(91, 33, 182, 0.4)", "avatar": "0 2px 4px rgba(0, 0, 0, 0.1)", "input": "0 0 0 3px rgba(91, 33, 182, 0.1)"}, "animations": {"cardHover": {"property": "transform, box-shadow", "duration": "0.2s", "timing": "ease"}, "buttonHover": {"property": "background-color, transform, box-shadow", "duration": "0.2s", "timing": "ease"}, "toggleSwitch": {"property": "background-color, transform", "duration": "0.2s", "timing": "ease"}}, "criticalRules": {"DO_NOT_APPLY": {"cardBackgrounds": ["text elements", "icons", "buttons"], "buttonStyles": ["text content", "card containers", "avatars"], "hoverEffects": ["static text", "decorative elements", "disabled states"], "gradients": ["text elements", "borders", "small icons"], "shadows": ["text content", "inline elements", "icon fills"], "transformations": ["text content", "static containers"]}, "ALWAYS_APPLY": {"transitions": ["interactive elements", "hover states", "focus states"], "focusStates": ["all interactive elements"], "properContrast": ["all text combinations", "icon and background pairs"], "semanticColors": ["status indicators", "action buttons", "navigation states"]}}, "contextualApplication": {"heroSection": {"backgroundTreatment": "Image with dark overlay gradient", "textTreatment": "White text with high contrast", "logoTreatment": "White/inverted logo filter"}, "sidebar": {"backgroundTreatment": "Clean white cards with subtle borders", "textTreatment": "Standard text hierarchy with purple accents", "interactiveTreatment": "Subtle hover states with color transitions"}, "navigation": {"backgroundTreatment": "Clean white with bottom border", "textTreatment": "Muted text with purple hover states", "iconTreatment": "Muted icons with color transitions"}}}