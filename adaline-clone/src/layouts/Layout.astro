---
export interface Props {
  title: string;
  description?: string;
}

const { title, description = "Connecting Africans across the continent and diaspora through truth, identity, and opportunity" } = Astro.props;
---

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <title>{title}</title>
  </head>
  <body class="bg-white text-gray-900 font-sans">
    <!-- Slide Menu Overlay -->
    <div class="slide-menu-overlay" id="slide-menu-overlay"></div>

    <!-- Slide Menu -->
    <div class="slide-menu" id="slide-menu">
      <!-- This will be populated by Header.astro script -->
    </div>

    <!-- Main App Container -->
    <div class="app-container" id="app-container">
      <slot />
    </div>
  </body>
</html>

<style is:global>
  html {
    font-family: system-ui, sans-serif;
    scroll-behavior: smooth;
  }

  body {
    margin: 0;
    padding: 0;
    line-height: 1.6;
  }

  /* Slide Menu Overlay */
  .slide-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0);
    transition: background-color 0.5s ease-out;
    pointer-events: none;
    z-index: 40;
  }

  body.slide-menu-open .slide-menu-overlay {
    background-color: rgba(0, 0, 0, 0.3);
    pointer-events: auto;
  }

  /* Slide Menu */
  .slide-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100%;
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    color: white;
    transform: translateX(-100%);
    transition: transform 0.5s ease-out;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    z-index: 50;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    overflow-y: auto;
    overflow-x: hidden;
  }

  body.slide-menu-open .slide-menu {
    transform: translateX(0);
  }

  /* Slide Menu Content Scrolling */
  .slide-menu .slide-menu-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flex item to shrink */
    overflow-y: auto;
    padding-bottom: 1rem; /* Extra padding at bottom for better UX */
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  }

  /* Custom scrollbar for slide menu */
  .slide-menu::-webkit-scrollbar {
    width: 6px;
  }

  .slide-menu::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  .slide-menu::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }

  .slide-menu::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
  }

  /* For Firefox */
  .slide-menu {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
  }

  /* Scroll indicator shadow */
  .slide-menu .slide-menu-content::after {
    content: '';
    position: sticky;
    bottom: 0;
    height: 20px;
    background: linear-gradient(transparent, rgba(31, 41, 55, 0.8));
    pointer-events: none;
    margin-top: -20px;
    z-index: 1;
  }

  /* App container for slide menu transformation */
  .app-container {
    min-height: 100vh;
    background-color: white;
    transition: transform 0.5s ease-out, border-radius 0.5s ease-out;
    position: relative;
    z-index: 10;
  }

  /* Transform entire app when menu is open */
  body.slide-menu-open .app-container {
    transform: scale(0.85) translateX(140px);
    border-radius: 1.5rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    overflow: hidden;
  }

  /* Change header positioning when menu is open */
  body.slide-menu-open header {
    position: relative !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
  }

  /* Prevent body scroll when menu is open */
  body.slide-menu-open {
    overflow: hidden;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .slide-menu {
      width: 70%;
    }

    body.slide-menu-open .app-container {
      transform: scale(0.9) translateX(calc(70vw / 2));
    }
  }

  @media (max-width: 480px) {
    .slide-menu {
      width: 85%;
    }

    body.slide-menu-open .app-container {
      transform: scale(0.9) translateX(calc(85vw / 2));
    }
  }

  /* Landscape mode optimizations */
  @media (max-height: 500px) and (orientation: landscape) {
    .slide-menu {
      padding: 1rem;
    }

    /* Reduce spacing in landscape mode */
    .slide-menu .slide-menu-brand {
      margin-bottom: 1rem !important;
      padding-bottom: 1rem !important;
    }

    .slide-menu .slide-menu-cta {
      margin-top: 1rem !important;
      padding-top: 1rem !important;
    }

    /* Make menu items more compact */
    .slide-menu .slide-menu-item {
      padding: 0.75rem 0.5rem !important;
      font-size: 1rem !important;
    }

    /* Ensure scrolling works well */
    .slide-menu {
      overflow-y: auto;
      -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    }
  }

  /* Very short screens (like phones in landscape) */
  @media (max-height: 400px) {
    .slide-menu {
      padding: 0.75rem;
    }

    .slide-menu .slide-menu-brand {
      margin-bottom: 0.75rem !important;
      padding-bottom: 0.75rem !important;
    }

    .slide-menu .slide-menu-item {
      padding: 0.5rem !important;
      font-size: 0.9rem !important;
    }

    .slide-menu .slide-menu-cta {
      margin-top: 0.75rem !important;
      padding-top: 0.75rem !important;
    }
  }

  /* Hide slide menu on large screens */
  @media (min-width: 1024px) {
    .slide-menu,
    .slide-menu-overlay {
      display: none !important;
    }
  }

  /* Accessibility - Reduced Motion */
  @media (prefers-reduced-motion: reduce) {
    .slide-menu,
    .slide-menu-overlay,
    .app-container {
      transition: none !important;
    }

    body.slide-menu-open .app-container {
      transform: none !important;
      border-radius: 0 !important;
    }
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
  }

  /* Smooth transitions for interactive elements */
  a, button {
    transition: all 0.2s ease-in-out;
  }

  /* Focus styles for accessibility */
  a:focus, button:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }
</style>
