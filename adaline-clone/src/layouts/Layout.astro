---
export interface Props {
  title: string;
  description?: string;
}

const { title, description = "Connecting Africans across the continent and diaspora through truth, identity, and opportunity" } = Astro.props;
---

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <title>{title}</title>
  </head>
  <body class="bg-white text-gray-900 font-sans">
    <slot />
  </body>
</html>

<style is:global>
  html {
    font-family: system-ui, sans-serif;
    scroll-behavior: smooth;
  }

  body {
    margin: 0;
    padding: 0;
    line-height: 1.6;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
  }

  /* Smooth transitions for interactive elements */
  a, button {
    transition: all 0.2s ease-in-out;
  }

  /* Focus styles for accessibility */
  a:focus, button:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }
</style>
