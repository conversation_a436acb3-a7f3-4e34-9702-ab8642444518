---
// Header component
---

<header class="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm">
  <nav class="max-w-7xl mx-auto px-3 sm:px-4 lg:px-8">
    <div class="flex justify-between items-center h-14 sm:h-16">
      <!-- Logo -->
      <div class="flex items-center flex-shrink-0">
        <a href="/" class="flex items-center space-x-2 sm:space-x-3">
          <div class="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-african-red to-african-gold rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-xs sm:text-sm">PJM</span>
          </div>
          <div class="flex flex-col">
            <span class="text-lg sm:text-xl font-bold text-gray-900 leading-tight">PJM Africa</span>
            <span class="text-xs text-gray-600 -mt-0.5 hidden sm:block">Yourself, Redefined</span>
          </div>
        </a>
      </div>

      <!-- Navigation Links -->
      <div class="hidden lg:flex items-center space-x-8">
        <div class="flex items-center space-x-6">
          <a href="/about" class="text-gray-600 hover:text-african-red transition-colors font-medium">About</a>
          <a href="/watch" class="text-gray-600 hover:text-african-red transition-colors font-medium">Watch</a>
          <a href="/listen" class="text-gray-600 hover:text-african-red transition-colors font-medium">Listen</a>
          <a href="/blog" class="text-gray-600 hover:text-african-red transition-colors font-medium">Read</a>
          <a href="/advisory" class="text-gray-600 hover:text-african-red transition-colors font-medium">Advisory</a>
          <a href="/join" class="text-gray-600 hover:text-african-red transition-colors font-medium">Join</a>
        </div>
      </div>

      <!-- CTA Buttons -->
      <div class="flex items-center space-x-2 sm:space-x-4">
        <a href="/watch" class="hidden lg:flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-african-red transition-colors">
          <span class="text-sm font-medium">Watch</span>
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
          </svg>
        </a>
        <a href="/join" class="bg-gradient-to-r from-african-red to-african-gold text-white px-3 py-2 sm:px-4 sm:py-2 rounded-lg hover:from-african-gold hover:to-african-red transition-all text-sm sm:text-base font-medium">
          <span class="hidden sm:inline">Join the Movement</span>
          <span class="sm:hidden">Join</span>
        </a>

        <!-- Mobile menu button -->
        <button
          class="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-african-red focus:ring-offset-2"
          id="mobile-menu-button"
          aria-label="Toggle mobile menu"
          aria-expanded="false"
        >
          <div class="hamburger-icon">
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
          </div>
        </button>
      </div>
    </div>

    <!-- Slide Menu Overlay -->
    <div class="lg:hidden slide-menu-overlay" id="slide-menu-overlay"></div>

    <!-- Slide Menu -->
    <div class="lg:hidden slide-menu" id="slide-menu">
      <!-- Close Button -->
      <button class="slide-menu-close" id="slide-menu-close" aria-label="Close menu">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>

      <!-- Menu Items -->
      <div class="slide-menu-content">
        <!-- Brand Section -->
        <div class="slide-menu-brand">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-r from-african-red to-african-gold rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-sm">PJM</span>
            </div>
            <div class="flex flex-col">
              <span class="text-xl font-bold text-white leading-tight">PJM Africa</span>
              <span class="text-sm text-gray-300 -mt-0.5">Yourself, Redefined</span>
            </div>
          </div>
        </div>

        <!-- Navigation Items -->
        <div class="slide-menu-items">
          <a href="/watch" class="slide-menu-item" data-name="Watch">
            <span class="slide-menu-icon">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
              </svg>
            </span>
            <span>Watch</span>
          </a>

          <a href="/listen" class="slide-menu-item" data-name="Listen">
            <span class="slide-menu-icon">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
              </svg>
            </span>
            <span>Listen</span>
          </a>

          <a href="/about" class="slide-menu-item" data-name="About">
            <span class="slide-menu-icon">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </span>
            <span>About</span>
          </a>

          <a href="/blog" class="slide-menu-item" data-name="Read">
            <span class="slide-menu-icon">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
              </svg>
            </span>
            <span>Read</span>
          </a>

          <a href="/advisory" class="slide-menu-item" data-name="Advisory">
            <span class="slide-menu-icon">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </span>
            <span>Advisory</span>
          </a>

          <a href="/join" class="slide-menu-item" data-name="Join">
            <span class="slide-menu-icon">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
              </svg>
            </span>
            <span>Join</span>
          </a>
        </div>

        <!-- CTA Section -->
        <div class="slide-menu-cta">
          <a href="/join" class="slide-menu-cta-button">
            <span>Join the Movement</span>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </nav>
</header>

<style>
  /* Hamburger menu animation */
  .hamburger-icon {
    width: 24px;
    height: 18px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .hamburger-line {
    width: 100%;
    height: 2px;
    background-color: #374151;
    border-radius: 1px;
    transition: all 0.3s ease;
    transform-origin: center;
  }

  .hamburger-icon.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
  }

  .hamburger-icon.active .hamburger-line:nth-child(2) {
    opacity: 0;
  }

  .hamburger-icon.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
  }

  /* Slide Menu Overlay */
  .slide-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0);
    transition: background-color 0.5s ease-out;
    pointer-events: none;
    z-index: 40;
  }

  body.slide-menu-open .slide-menu-overlay {
    background-color: rgba(0, 0, 0, 0.3);
    pointer-events: auto;
  }

  /* Slide Menu */
  .slide-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100%;
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    color: white;
    transform: translateX(-100%);
    transition: transform 0.5s ease-out;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    z-index: 50;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  }

  body.slide-menu-open .slide-menu {
    transform: translateX(0);
  }

  /* Main content transformation when menu is open */
  body.slide-menu-open {
    overflow: hidden;
  }

  /* Create app container wrapper for proper transformation */
  body.slide-menu-open .app-wrapper {
    transform: scale(0.85) translateX(140px);
    border-radius: 1.5rem;
    transition: transform 0.5s ease-out, border-radius 0.5s ease-out;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  }

  /* Fallback for individual elements if app-wrapper doesn't exist */
  body.slide-menu-open header,
  body.slide-menu-open main,
  body.slide-menu-open section {
    transform: scale(0.85) translateX(140px);
    border-radius: 1.5rem;
    transition: transform 0.5s ease-out, border-radius 0.5s ease-out;
    overflow: hidden;
  }

  /* Close Button */
  .slide-menu-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    align-self: flex-end;
    margin-bottom: 2rem;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateX(20px);
  }

  .slide-menu-close:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
  }

  body.slide-menu-open .slide-menu-close {
    opacity: 1;
    transform: translateX(0);
    transition-delay: 0.1s;
  }

  /* Menu Content */
  .slide-menu-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  /* Brand Section */
  .slide-menu-brand {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0;
    transform: translateX(-20px);
    transition: all 0.3s ease;
  }

  body.slide-menu-open .slide-menu-brand {
    opacity: 1;
    transform: translateX(0);
    transition-delay: 0.15s;
  }

  /* Menu Items Container */
  .slide-menu-items {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  /* Individual Menu Items */
  .slide-menu-item {
    display: flex;
    align-items: center;
    padding: 1rem 0.75rem;
    color: white;
    text-decoration: none;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateX(-20px);
    font-weight: 500;
    font-size: 1.125rem;
  }

  .slide-menu-item:hover {
    background: linear-gradient(135deg, rgba(220, 38, 38, 0.2), rgba(245, 158, 11, 0.2));
    transform: translateX(5px);
  }

  .slide-menu-icon {
    width: 24px;
    margin-right: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Staggered animation for menu items */
  body.slide-menu-open .slide-menu-item:nth-child(1) {
    opacity: 1;
    transform: translateX(0);
    transition-delay: 0.2s;
  }

  body.slide-menu-open .slide-menu-item:nth-child(2) {
    opacity: 1;
    transform: translateX(0);
    transition-delay: 0.25s;
  }

  body.slide-menu-open .slide-menu-item:nth-child(3) {
    opacity: 1;
    transform: translateX(0);
    transition-delay: 0.3s;
  }

  body.slide-menu-open .slide-menu-item:nth-child(4) {
    opacity: 1;
    transform: translateX(0);
    transition-delay: 0.35s;
  }

  body.slide-menu-open .slide-menu-item:nth-child(5) {
    opacity: 1;
    transform: translateX(0);
    transition-delay: 0.4s;
  }

  body.slide-menu-open .slide-menu-item:nth-child(6) {
    opacity: 1;
    transform: translateX(0);
    transition-delay: 0.45s;
  }

  /* CTA Section */
  .slide-menu-cta {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0;
    transform: translateX(-20px);
    transition: all 0.3s ease;
  }

  body.slide-menu-open .slide-menu-cta {
    opacity: 1;
    transform: translateX(0);
    transition-delay: 0.5s;
  }

  .slide-menu-cta-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #dc2626, #f59e0b);
    color: white;
    text-decoration: none;
    border-radius: 0.75rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
  }

  .slide-menu-cta-button:hover {
    background: linear-gradient(135deg, #f59e0b, #dc2626);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .slide-menu {
      width: 70%;
    }

    body.slide-menu-open .app-wrapper {
      transform: scale(0.9) translateX(calc(70vw / 2));
    }

    body.slide-menu-open header,
    body.slide-menu-open main,
    body.slide-menu-open section {
      transform: scale(0.9) translateX(calc(70vw / 2));
    }
  }

  @media (max-width: 480px) {
    .slide-menu {
      width: 85%;
    }

    body.slide-menu-open .app-wrapper {
      transform: scale(0.9) translateX(calc(85vw / 2));
    }

    body.slide-menu-open header,
    body.slide-menu-open main,
    body.slide-menu-open section {
      transform: scale(0.9) translateX(calc(85vw / 2));
    }

    .slide-menu-item {
      font-size: 1rem;
      padding: 0.875rem 0.75rem;
    }

    .slide-menu-icon {
      width: 20px;
      margin-right: 0.875rem;
    }
  }

  /* Accessibility - Reduced Motion */
  @media (prefers-reduced-motion: reduce) {
    .slide-menu,
    .slide-menu-overlay,
    .slide-menu-close,
    .slide-menu-brand,
    .slide-menu-item,
    .slide-menu-cta,
    .slide-menu-cta-button {
      transition: none !important;
    }

    body.slide-menu-open .app-wrapper,
    body.slide-menu-open header,
    body.slide-menu-open main,
    body.slide-menu-open section {
      transform: none !important;
      border-radius: 0 !important;
    }
  }

  /* Focus styles for accessibility */
  .slide-menu-item:focus,
  .slide-menu-cta-button:focus,
  .slide-menu-close:focus {
    outline: 2px solid #f59e0b;
    outline-offset: 2px;
  }
</style>

<script>
  // Slide menu functionality
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const slideMenu = document.getElementById('slide-menu');
  const slideMenuClose = document.getElementById('slide-menu-close');
  const slideMenuOverlay = document.getElementById('slide-menu-overlay');
  const hamburgerIcon = mobileMenuButton?.querySelector('.hamburger-icon');
  let isMenuOpen = false;

  function openSlideMenu() {
    isMenuOpen = true;
    document.body.classList.add('slide-menu-open');
    hamburgerIcon?.classList.add('active');
    mobileMenuButton?.setAttribute('aria-expanded', 'true');

    // Focus management for accessibility
    slideMenuClose?.focus();
  }

  function closeSlideMenu() {
    isMenuOpen = false;
    document.body.classList.remove('slide-menu-open');
    hamburgerIcon?.classList.remove('active');
    mobileMenuButton?.setAttribute('aria-expanded', 'false');

    // Return focus to menu button
    mobileMenuButton?.focus();
  }

  function toggleSlideMenu() {
    if (isMenuOpen) {
      closeSlideMenu();
    } else {
      openSlideMenu();
    }
  }

  // Event listeners
  mobileMenuButton?.addEventListener('click', toggleSlideMenu);
  slideMenuClose?.addEventListener('click', closeSlideMenu);
  slideMenuOverlay?.addEventListener('click', closeSlideMenu);

  // Close menu when clicking on menu items
  const slideMenuItems = slideMenu?.querySelectorAll('.slide-menu-item');
  slideMenuItems?.forEach(item => {
    item.addEventListener('click', () => {
      if (isMenuOpen) {
        closeSlideMenu();
      }
    });
  });

  // Close menu on escape key
  document.addEventListener('keydown', (event) => {
    if (event.key === 'Escape' && isMenuOpen) {
      closeSlideMenu();
    }
  });

  // Handle window resize
  window.addEventListener('resize', () => {
    if (window.innerWidth >= 1024 && isMenuOpen) {
      closeSlideMenu();
    }
  });

  // Trap focus within menu when open
  document.addEventListener('keydown', (event) => {
    if (!isMenuOpen || event.key !== 'Tab') return;

    const focusableElements = slideMenu?.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    if (!focusableElements || focusableElements.length === 0) return;

    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    if (event.shiftKey) {
      if (document.activeElement === firstElement) {
        event.preventDefault();
        lastElement.focus();
      }
    } else {
      if (document.activeElement === lastElement) {
        event.preventDefault();
        firstElement.focus();
      }
    }
  });

  // Log menu item clicks for analytics (optional)
  slideMenuItems?.forEach(item => {
    item.addEventListener('click', () => {
      const itemName = item.getAttribute('data-name');
      console.log(`Slide menu item clicked: ${itemName}`);
    });
  });
</script>
