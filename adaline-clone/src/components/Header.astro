---
// Header component
---

<header class="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm">
  <nav class="max-w-7xl mx-auto px-3 sm:px-4 lg:px-8">
    <div class="flex justify-between items-center h-14 sm:h-16">
      <!-- Logo -->
      <div class="flex items-center flex-shrink-0">
        <a href="/" class="flex items-center space-x-2 sm:space-x-3">
          <div class="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-african-red to-african-gold rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-xs sm:text-sm">PJM</span>
          </div>
          <div class="flex flex-col">
            <span class="text-lg sm:text-xl font-bold text-gray-900 leading-tight">PJM Africa</span>
            <span class="text-xs text-gray-600 -mt-0.5 hidden sm:block">Yourself, Redefined</span>
          </div>
        </a>
      </div>

      <!-- Navigation Links -->
      <div class="hidden lg:flex items-center space-x-8">
        <div class="flex items-center space-x-6">
          <a href="/about" class="text-gray-600 hover:text-african-red transition-colors font-medium">About</a>
          <a href="/watch" class="text-gray-600 hover:text-african-red transition-colors font-medium">Watch</a>
          <a href="/listen" class="text-gray-600 hover:text-african-red transition-colors font-medium">Listen</a>
          <a href="/blog" class="text-gray-600 hover:text-african-red transition-colors font-medium">Read</a>
          <a href="/advisory" class="text-gray-600 hover:text-african-red transition-colors font-medium">Advisory</a>
        </div>
      </div>

      <!-- CTA Buttons -->
      <div class="flex items-center space-x-2 sm:space-x-4">
        <a href="/watch" class="hidden lg:flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-african-red transition-colors">
          <span class="text-sm font-medium">Watch</span>
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
          </svg>
        </a>
        <a href="/join" class="bg-gradient-to-r from-african-red to-african-gold text-white px-3 py-2 sm:px-4 sm:py-2 rounded-lg hover:from-african-gold hover:to-african-red transition-all text-sm sm:text-base font-medium">
          <span class="hidden sm:inline">Join the Movement</span>
          <span class="sm:hidden">Join</span>
        </a>

        <!-- Mobile menu button -->
        <button
          class="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-african-red focus:ring-offset-2"
          id="mobile-menu-button"
          aria-label="Toggle mobile menu"
          aria-expanded="false"
        >
          <div class="hamburger-icon">
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
          </div>
        </button>
      </div>
    </div>


  </nav>
</header>

<style>
  /* Hamburger menu animation */
  .hamburger-icon {
    width: 24px;
    height: 18px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .hamburger-line {
    width: 100%;
    height: 2px;
    background-color: #374151;
    border-radius: 1px;
    transition: all 0.3s ease;
    transform-origin: center;
  }

  .hamburger-icon.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
  }

  .hamburger-icon.active .hamburger-line:nth-child(2) {
    opacity: 0;
  }

  .hamburger-icon.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
  }
</style>

<script>
  // Wait for DOM to be ready
  document.addEventListener('DOMContentLoaded', () => {
    // Get elements
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const slideMenu = document.getElementById('slide-menu');
    const slideMenuOverlay = document.getElementById('slide-menu-overlay');
    const hamburgerIcon = mobileMenuButton?.querySelector('.hamburger-icon');
    let isMenuOpen = false;

    // Create slide menu content
    function createSlideMenuContent() {
      if (!slideMenu) return;

      slideMenu.innerHTML = `
        <!-- Close Button -->
        <button class="slide-menu-close" id="slide-menu-close" aria-label="Close menu" style="background: none; border: none; color: white; cursor: pointer; align-self: flex-end; margin-bottom: 2rem; padding: 0.5rem; border-radius: 0.5rem; transition: all 0.3s ease; opacity: 0; transform: translateX(20px);">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>

        <!-- Brand Section (Fixed at top) -->
        <div class="slide-menu-brand" style="margin-bottom: 2rem; padding-bottom: 1.5rem; border-bottom: 1px solid rgba(255, 255, 255, 0.1); opacity: 0; transform: translateX(-20px); transition: all 0.3s ease; flex-shrink: 0;">
          <a href="/" class="flex items-center space-x-2 sm:space-x-3">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-r from-red-600 to-yellow-500 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-sm">PJM</span>
            </div>
            <div class="flex flex-col">
              <span class="text-xl font-bold text-white leading-tight">PJM Africa</span>
              <span class="text-sm text-gray-300 -mt-0.5">Yourself, Redefined</span>
            </div>
          </div>
          </a>
        </div>

        <!-- Scrollable Navigation Items -->
        <div class="slide-menu-content" style="flex: 1; display: flex; flex-direction: column; min-height: 0; overflow-y: auto; -webkit-overflow-scrolling: touch;">
          <div class="slide-menu-items" style="display: flex; flex-direction: column; gap: 0.5rem; padding-bottom: 1rem;">
            <a href="/watch" class="slide-menu-item" data-name="Watch" style="display: flex; align-items: center; padding: 1rem 0.75rem; color: white; text-decoration: none; border-radius: 0.75rem; transition: all 0.3s ease; opacity: 0; transform: translateX(-20px); font-weight: 500; font-size: 1.125rem;">
              <span class="slide-menu-icon" style="width: 24px; margin-right: 1rem; display: flex; align-items: center; justify-content: center;">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
              </span>
              <span>Watch</span>
            </a>

            <a href="/listen" class="slide-menu-item" data-name="Listen" style="display: flex; align-items: center; padding: 1rem 0.75rem; color: white; text-decoration: none; border-radius: 0.75rem; transition: all 0.3s ease; opacity: 0; transform: translateX(-20px); font-weight: 500; font-size: 1.125rem;">
              <span class="slide-menu-icon" style="width: 24px; margin-right: 1rem; display: flex; align-items: center; justify-content: center;">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                </svg>
              </span>
              <span>Listen</span>
            </a>

            <a href="/about" class="slide-menu-item" data-name="About" style="display: flex; align-items: center; padding: 1rem 0.75rem; color: white; text-decoration: none; border-radius: 0.75rem; transition: all 0.3s ease; opacity: 0; transform: translateX(-20px); font-weight: 500; font-size: 1.125rem;">
              <span class="slide-menu-icon" style="width: 24px; margin-right: 1rem; display: flex; align-items: center; justify-content: center;">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </span>
              <span>About</span>
            </a>

            <a href="/blog" class="slide-menu-item" data-name="Read" style="display: flex; align-items: center; padding: 1rem 0.75rem; color: white; text-decoration: none; border-radius: 0.75rem; transition: all 0.3s ease; opacity: 0; transform: translateX(-20px); font-weight: 500; font-size: 1.125rem;">
              <span class="slide-menu-icon" style="width: 24px; margin-right: 1rem; display: flex; align-items: center; justify-content: center;">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
              </span>
              <span>Read</span>
            </a>

            <a href="/advisory" class="slide-menu-item" data-name="Advisory" style="display: flex; align-items: center; padding: 1rem 0.75rem; color: white; text-decoration: none; border-radius: 0.75rem; transition: all 0.3s ease; opacity: 0; transform: translateX(-20px); font-weight: 500; font-size: 1.125rem;">
              <span class="slide-menu-icon" style="width: 24px; margin-right: 1rem; display: flex; align-items: center; justify-content: center;">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
              </span>
              <span>Advisory</span>
            </a>

          </div>
        </div>

        <!-- CTA Section (Fixed at bottom) -->
        <div class="slide-menu-cta" style="margin-top: 2rem; padding-top: 1.5rem; border-top: 1px solid rgba(255, 255, 255, 0.1); opacity: 0; transform: translateX(-20px); transition: all 0.3s ease; flex-shrink: 0;">
          <a href="/join" class="slide-menu-cta-button" style="display: flex; align-items: center; justify-content: space-between; width: 100%; padding: 1rem 1.5rem; background: linear-gradient(135deg, #dc2626, #f59e0b); color: white; text-decoration: none; border-radius: 0.75rem; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);">
            <span>Join the Movement</span>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
            </svg>
          </a>
        </div>
      `;
    }

    // Create the menu content
    createSlideMenuContent();

    // Get the close button after creating content
    const slideMenuClose = document.getElementById('slide-menu-close');

    function openSlideMenu() {
      isMenuOpen = true;
      document.body.classList.add('slide-menu-open');
      hamburgerIcon?.classList.add('active');
      mobileMenuButton?.setAttribute('aria-expanded', 'true');

      // Animate menu items
      setTimeout(() => {
        const brand = slideMenu?.querySelector('.slide-menu-brand') as HTMLElement;
        const items = slideMenu?.querySelectorAll('.slide-menu-item') as NodeListOf<HTMLElement>;
        const cta = slideMenu?.querySelector('.slide-menu-cta') as HTMLElement;
        const closeBtn = slideMenu?.querySelector('.slide-menu-close') as HTMLElement;

        if (closeBtn) closeBtn.style.cssText += 'opacity: 1; transform: translateX(0); transition-delay: 0.1s;';
        if (brand) brand.style.cssText += 'opacity: 1; transform: translateX(0); transition-delay: 0.15s;';

        items?.forEach((item, index) => {
          const delay = 0.2 + (index * 0.05);
          item.style.cssText += `opacity: 1; transform: translateX(0); transition-delay: ${delay}s;`;
        });

        if (cta) cta.style.cssText += 'opacity: 1; transform: translateX(0); transition-delay: 0.5s;';
      }, 50);

      // Focus management for accessibility
      slideMenuClose?.focus();
    }

    function closeSlideMenu() {
      isMenuOpen = false;
      document.body.classList.remove('slide-menu-open');
      hamburgerIcon?.classList.remove('active');
      mobileMenuButton?.setAttribute('aria-expanded', 'false');

      // Reset menu item animations
      const brand = slideMenu?.querySelector('.slide-menu-brand') as HTMLElement;
      const items = slideMenu?.querySelectorAll('.slide-menu-item') as NodeListOf<HTMLElement>;
      const cta = slideMenu?.querySelector('.slide-menu-cta') as HTMLElement;
      const closeBtn = slideMenu?.querySelector('.slide-menu-close') as HTMLElement;

      if (closeBtn) closeBtn.style.cssText = closeBtn.style.cssText.replace(/opacity: 1; transform: translateX\(0\);[^;]*;/, 'opacity: 0; transform: translateX(20px);');
      if (brand) brand.style.cssText = brand.style.cssText.replace(/opacity: 1; transform: translateX\(0\);[^;]*;/, 'opacity: 0; transform: translateX(-20px);');

      items?.forEach((item) => {
        item.style.cssText = item.style.cssText.replace(/opacity: 1; transform: translateX\(0\);[^;]*;/, 'opacity: 0; transform: translateX(-20px);');
      });

      if (cta) cta.style.cssText = cta.style.cssText.replace(/opacity: 1; transform: translateX\(0\);[^;]*;/, 'opacity: 0; transform: translateX(-20px);');

      // Return focus to menu button
      mobileMenuButton?.focus();
    }

    function toggleSlideMenu() {
      if (isMenuOpen) {
        closeSlideMenu();
      } else {
        openSlideMenu();
      }
    }

    // Event listeners
    mobileMenuButton?.addEventListener('click', toggleSlideMenu);
    slideMenuClose?.addEventListener('click', closeSlideMenu);
    slideMenuOverlay?.addEventListener('click', closeSlideMenu);

    // Close menu when clicking on menu items
    slideMenu?.addEventListener('click', (event) => {
      const target = event.target && (event.target as HTMLElement).closest('.slide-menu-item');
      if (target && isMenuOpen) {
        closeSlideMenu();
      }
    });

    // Close menu on escape key
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape' && isMenuOpen) {
        closeSlideMenu();
      }
    });

    // Handle window resize
    window.addEventListener('resize', () => {
      if (window.innerWidth >= 1024 && isMenuOpen) {
        closeSlideMenu();
      }
    });
  });
</script>
