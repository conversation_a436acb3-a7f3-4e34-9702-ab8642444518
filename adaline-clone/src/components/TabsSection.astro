---
// Interactive tabs section component
---

<section class="py-20 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <!-- Left side - Tabs -->
      <div>
        <div class="space-y-6">
          <!-- Tab Navigation -->
          <div class="space-y-4" id="tab-navigation">
            <!-- Iterate Tab -->
            <div class="tab-item active" data-tab="iterate">
              <div class="flex items-center space-x-4 p-6 rounded-xl border-2 border-blue-200 bg-blue-50 cursor-pointer transition-all duration-200 hover:border-blue-300">
                <div class="flex items-center space-x-3">
                  <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                  </div>
                  <div class="flex-1">
                    <div class="flex items-center space-x-2">
                      <h3 class="text-lg font-semibold text-gray-900">Iterate</h3>
                      <span class="text-sm text-blue-600 font-medium">01</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Evaluate Tab -->
            <div class="tab-item" data-tab="evaluate">
              <div class="flex items-center space-x-4 p-6 rounded-xl border-2 border-gray-200 bg-gray-50 cursor-pointer transition-all duration-200 hover:border-gray-300">
                <div class="flex items-center space-x-3">
                  <div class="w-12 h-12 bg-gray-400 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                  </div>
                  <div class="flex-1">
                    <div class="flex items-center space-x-2">
                      <h3 class="text-lg font-semibold text-gray-900">Evaluate</h3>
                      <span class="text-sm text-gray-500 font-medium">02</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Deploy Tab -->
            <div class="tab-item" data-tab="deploy">
              <div class="flex items-center space-x-4 p-6 rounded-xl border-2 border-gray-200 bg-gray-50 cursor-pointer transition-all duration-200 hover:border-gray-300">
                <div class="flex items-center space-x-3">
                  <div class="w-12 h-12 bg-gray-400 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                  </div>
                  <div class="flex-1">
                    <div class="flex items-center space-x-2">
                      <h3 class="text-lg font-semibold text-gray-900">Deploy</h3>
                      <span class="text-sm text-gray-500 font-medium">03</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Monitor Tab -->
            <div class="tab-item" data-tab="monitor">
              <div class="flex items-center space-x-4 p-6 rounded-xl border-2 border-gray-200 bg-gray-50 cursor-pointer transition-all duration-200 hover:border-gray-300">
                <div class="flex items-center space-x-3">
                  <div class="w-12 h-12 bg-gray-400 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                  </div>
                  <div class="flex-1">
                    <div class="flex items-center space-x-2">
                      <h3 class="text-lg font-semibold text-gray-900">Monitor</h3>
                      <span class="text-sm text-gray-500 font-medium">04</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Learn More Links -->
          <div class="pt-6">
            <p class="text-sm text-gray-500 mb-3">Learn more</p>
            <div class="flex flex-wrap gap-4">
              <a href="/editor" class="text-blue-600 hover:text-blue-700 text-sm font-medium">Editor</a>
              <a href="/datasets" class="text-blue-600 hover:text-blue-700 text-sm font-medium">Datasets</a>
            </div>
          </div>
        </div>
      </div>

      <!-- Right side - Tab Content -->
      <div class="relative">
        <!-- Iterate Content -->
        <div class="tab-content active" id="iterate-content">
          <div class="bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Prompt management across providers</h3>
            <p class="text-gray-600 mb-6">
              Centralize your prompts for all LLM providers in one intuitive workspace, eliminating fragmentation and ensuring consistency across your AI applications.
            </p>
            <div class="bg-white rounded-xl p-6 shadow-sm">
              <div class="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-4"></div>
              <div class="text-sm text-gray-500">Interactive prompt editor interface</div>
            </div>
          </div>
        </div>

        <!-- Other tab contents (hidden by default) -->
        <div class="tab-content hidden" id="evaluate-content">
          <div class="bg-gradient-to-br from-green-50 to-blue-50 rounded-2xl p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Comprehensive evaluation framework</h3>
            <p class="text-gray-600 mb-6">
              Test and validate your AI models with robust evaluation metrics and automated testing pipelines.
            </p>
            <div class="bg-white rounded-xl p-6 shadow-sm">
              <div class="w-full h-48 bg-gradient-to-br from-green-100 to-blue-100 rounded-lg mb-4"></div>
              <div class="text-sm text-gray-500">Evaluation dashboard and metrics</div>
            </div>
          </div>
        </div>

        <div class="tab-content hidden" id="deploy-content">
          <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Seamless deployment pipeline</h3>
            <p class="text-gray-600 mb-6">
              Deploy your AI models to production with confidence using our automated deployment infrastructure.
            </p>
            <div class="bg-white rounded-xl p-6 shadow-sm">
              <div class="w-full h-48 bg-gradient-to-br from-purple-100 to-pink-100 rounded-lg mb-4"></div>
              <div class="text-sm text-gray-500">Deployment configuration interface</div>
            </div>
          </div>
        </div>

        <div class="tab-content hidden" id="monitor-content">
          <div class="bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Real-time monitoring and analytics</h3>
            <p class="text-gray-600 mb-6">
              Monitor your AI applications in real-time with comprehensive analytics and performance insights.
            </p>
            <div class="bg-white rounded-xl p-6 shadow-sm">
              <div class="w-full h-48 bg-gradient-to-br from-orange-100 to-red-100 rounded-lg mb-4"></div>
              <div class="text-sm text-gray-500">Monitoring dashboard and alerts</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  // Tab switching functionality
  document.addEventListener('DOMContentLoaded', function() {
    const tabItems = document.querySelectorAll('.tab-item');
    const tabContents = document.querySelectorAll('.tab-content');

    tabItems.forEach(item => {
      item.addEventListener('click', function() {
        const tabName = this.dataset.tab;
        
        // Remove active class from all tabs
        tabItems.forEach(tab => {
          tab.classList.remove('active');
          const tabDiv = tab.querySelector('div');
          tabDiv.classList.remove('border-blue-200', 'bg-blue-50');
          tabDiv.classList.add('border-gray-200', 'bg-gray-50');
          
          const icon = tab.querySelector('.w-12');
          icon.classList.remove('bg-blue-600');
          icon.classList.add('bg-gray-400');
          
          const number = tab.querySelector('span');
          number.classList.remove('text-blue-600');
          number.classList.add('text-gray-500');
        });
        
        // Add active class to clicked tab
        this.classList.add('active');
        const activeTabDiv = this.querySelector('div');
        activeTabDiv.classList.remove('border-gray-200', 'bg-gray-50');
        activeTabDiv.classList.add('border-blue-200', 'bg-blue-50');
        
        const activeIcon = this.querySelector('.w-12');
        activeIcon.classList.remove('bg-gray-400');
        activeIcon.classList.add('bg-blue-600');
        
        const activeNumber = this.querySelector('span');
        activeNumber.classList.remove('text-gray-500');
        activeNumber.classList.add('text-blue-600');
        
        // Hide all tab contents
        tabContents.forEach(content => {
          content.classList.add('hidden');
          content.classList.remove('active');
        });
        
        // Show selected tab content
        const activeContent = document.getElementById(tabName + '-content');
        if (activeContent) {
          activeContent.classList.remove('hidden');
          activeContent.classList.add('active');
        }
      });
    });
  });
</script>
