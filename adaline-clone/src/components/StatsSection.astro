---
// Statistics section component
---

<section class="py-20 bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Section Header -->
    <div class="text-center mb-16">
      <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
        Building Africa's largest storytelling platform
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        PJM Africa connects millions of Africans across the continent and diaspora through authentic stories, cultural insights, and shared experiences.
      </p>
    </div>

    <!-- Statistics Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      <!-- Countries Stat -->
      <div class="bg-white rounded-2xl p-8 shadow-sm border border-gray-200 text-center">
        <div class="mb-4">
          <div class="text-4xl md:text-5xl font-bold text-african-red mb-2">
            <span class="counter" data-target="32">0</span>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Countries Visited</h3>
          <p class="text-gray-600 text-sm">Across all regions of Africa</p>
        </div>
      </div>

      <!-- Episodes Stat -->
      <div class="bg-white rounded-2xl p-8 shadow-sm border border-gray-200 text-center">
        <div class="mb-4">
          <div class="text-4xl md:text-5xl font-bold text-african-gold mb-2">
            <span class="counter" data-target="100">0</span>+
          </div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Episodes Created</h3>
          <p class="text-gray-600 text-sm">Documentary content and interviews</p>
        </div>
      </div>

      <!-- Community Stat -->
      <div class="bg-white rounded-2xl p-8 shadow-sm border border-gray-200 text-center">
        <div class="mb-4">
          <div class="text-4xl md:text-5xl font-bold text-african-green mb-2">
            <span class="counter" data-target="50">0</span>K+
          </div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Community Members</h3>
          <p class="text-gray-600 text-sm">Africans connected worldwide</p>
        </div>
      </div>

      <!-- Journey Stat -->
      <div class="bg-white rounded-2xl p-8 shadow-sm border border-gray-200 text-center">
        <div class="mb-4">
          <div class="text-4xl md:text-5xl font-bold text-african-sunset mb-2">
            <span class="counter" data-target="36">0</span>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Weeks of Journey</h3>
          <p class="text-gray-600 text-sm">Documenting Africa's stories</p>
        </div>
      </div>
    </div>

    <!-- Trust Indicators -->
    <div class="mt-20">
      <div class="bg-white rounded-2xl p-12 shadow-sm border border-gray-200">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <!-- Left side - Content -->
          <div>
            <h3 class="text-3xl font-bold text-gray-900 mb-6">
              For Africans across the continent and diaspora
            </h3>
            <p class="text-lg text-gray-600 mb-8">
              PJM Africa is the platform connecting Africans worldwide through authentic storytelling, cultural insights, and shared experiences that celebrate our heritage and shape our future.
            </p>
            <a href="/join"
               class="inline-flex items-center bg-gradient-to-r from-african-red to-african-gold text-white px-6 py-3 rounded-lg hover:from-african-gold hover:to-african-red transition-all font-medium">
              Join the Movement
              <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </a>
          </div>

          <!-- Right side - Features -->
          <div class="space-y-8">
            <!-- Authentic Stories -->
            <div class="flex items-start space-x-4">
              <div class="w-12 h-12 bg-african-red/10 rounded-lg flex items-center justify-center flex-shrink-0">
                <svg class="w-6 h-6 text-african-red" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V1a1 1 0 011-1h2a1 1 0 011 1v3m0 0h8m-8 0V4a1 1 0 011-1h6a1 1 0 011 1v0"></path>
                </svg>
              </div>
              <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-2">Authentic Stories</h4>
                <p class="text-gray-600">Real voices from across Africa</p>
              </div>
            </div>

            <!-- Cultural Connection -->
            <div class="flex items-start space-x-4">
              <div class="w-12 h-12 bg-african-gold/10 rounded-lg flex items-center justify-center flex-shrink-0">
                <svg class="w-6 h-6 text-african-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
                </svg>
              </div>
              <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-2">Cultural Connection</h4>
                <p class="text-gray-600">Bridging continent and diaspora</p>
              </div>
            </div>

            <!-- Community Impact -->
            <div class="flex items-start space-x-4">
              <div class="w-12 h-12 bg-african-green/10 rounded-lg flex items-center justify-center flex-shrink-0">
                <svg class="w-6 h-6 text-african-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
              </div>
              <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-2">Community Impact</h4>
                <p class="text-gray-600">Building unity through shared experiences</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Mission Values -->
        <div class="mt-12 pt-8 border-t border-gray-200">
          <div class="flex justify-center items-center space-x-8">
            <div class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-african-red/10 rounded flex items-center justify-center">
                <svg class="w-4 h-4 text-african-red" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <span class="text-sm font-medium text-gray-700">Truth & Authenticity</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-african-gold/10 rounded flex items-center justify-center">
                <svg class="w-4 h-4 text-african-gold" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <span class="text-sm font-medium text-gray-700">Cultural Identity</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-african-green/10 rounded flex items-center justify-center">
                <svg class="w-4 h-4 text-african-green" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <span class="text-sm font-medium text-gray-700">Unity & Opportunity</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  // Animated counter functionality
  function animateCounters() {
    const counters = document.querySelectorAll('.counter');
    
    counters.forEach(counter => {
      const target = parseFloat(counter.getAttribute('data-target'));
      const duration = 2000; // 2 seconds
      const increment = target / (duration / 16); // 60fps
      let current = 0;
      
      const updateCounter = () => {
        if (current < target) {
          current += increment;
          if (current > target) current = target;
          
          // Format the number based on the target
          if (target >= 1000) {
            counter.textContent = Math.floor(current).toLocaleString();
          } else if (target > 99) {
            counter.textContent = Math.floor(current);
          } else {
            counter.textContent = current.toFixed(3);
          }
          
          requestAnimationFrame(updateCounter);
        } else {
          // Final formatting
          if (target >= 1000) {
            counter.textContent = target.toLocaleString();
          } else if (target > 99) {
            counter.textContent = target;
          } else {
            counter.textContent = target;
          }
        }
      };
      
      updateCounter();
    });
  }

  // Intersection Observer to trigger animation when section is visible
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        animateCounters();
        observer.unobserve(entry.target);
      }
    });
  }, { threshold: 0.5 });

  document.addEventListener('DOMContentLoaded', () => {
    const statsSection = document.querySelector('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4');
    if (statsSection) {
      observer.observe(statsSection);
    }
  });
</script>
