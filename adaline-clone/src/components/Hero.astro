---
// Dynamic Hero section with scroll-triggered image carousel
---

<section class="dynamic-hero relative overflow-hidden" id="dynamic-hero">
  <!-- Background Image Container -->
  <div class="hero-bg-container absolute inset-0 z-0">
    <div class="hero-bg-image active" data-image="/images/hero/Cape-Town.jpg" style="background-image: url('/images/hero/Cape-Town.jpg')"></div>
    <div class="hero-bg-image" data-image="/images/hero/kenya.jpg" style="background-image: url('/images/hero/kenya.jpg')"></div>
    <div class="hero-bg-image" data-image="/images/hero/nigeria1.jpg" style="background-image: url('/images/hero/nigeria1.jpg')"></div>
    <div class="hero-bg-image" data-image="/images/hero/nigeria2.webp" style="background-image: url('/images/hero/nigeria2.webp')"></div>
    <div class="hero-bg-image" data-image="/images/hero/south-africa.jpg" style="background-image: url('/images/hero/south-africa.jpg')"></div>
  </div>

  <!-- Overlay for better text readability -->
  <div class="absolute inset-0 bg-black/40 z-10"></div>

  <!-- Content Container -->
  <div class="relative z-20 min-h-screen flex items-center">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">

      <!-- Announcement Banner -->
      <div class="text-center mb-8 pt-24">
        <a href="/about"
           class="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-md border border-white/20 rounded-full px-6 py-3 text-sm font-medium text-white hover:bg-white/20 transition-all duration-200">
          <span class="text-lg">🌍</span>
          <span>32 Countries. 36 Weeks. One Mission: Africa United.</span>
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </a>
      </div>

      <!-- Dynamic Text Content Sections -->
      <div class="hero-content-container text-center max-w-4xl mx-auto">

        <!-- Content Section 1: Cape Town -->
        <div class="hero-content-section active" data-section="0">
          <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold text-white leading-tight mb-6">
            Connecting Africans through
            <span class="bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
              truth, identity, and opportunity
            </span>
          </h1>
          <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
            Documentary content, podcasts, and cultural insights to shift the narrative, reclaim our power, and rebuild a united African future — by us, for us.
          </p>
          <div class="mb-8">
            <p class="text-lg font-medium text-white mb-4">This isn't just media. This is a mission.</p>
          </div>
        </div>

        <!-- Content Section 2: Kenya -->
        <div class="hero-content-section" data-section="1">
          <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold text-white leading-tight mb-6">
            From the Heart of
            <span class="bg-gradient-to-r from-green-400 to-yellow-400 bg-clip-text text-transparent">
              East Africa
            </span>
          </h1>
          <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
            Discover the entrepreneurial spirit and innovation hubs that are shaping Kenya's future and inspiring the continent.
          </p>
          <div class="mb-8">
            <p class="text-lg font-medium text-white mb-4">Where tradition meets innovation.</p>
          </div>
        </div>

        <!-- Content Section 3: Nigeria -->
        <div class="hero-content-section" data-section="2">
          <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold text-white leading-tight mb-6">
            The Giant of
            <span class="bg-gradient-to-r from-green-400 to-white bg-clip-text text-transparent">
              West Africa
            </span>
          </h1>
          <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
            Experience Nigeria's vibrant culture, Nollywood magic, and the unstoppable energy of Africa's most populous nation.
          </p>
          <div class="mb-8">
            <p class="text-lg font-medium text-white mb-4">Where dreams become reality.</p>
          </div>
        </div>

        <!-- Content Section 4: Nigeria Culture -->
        <div class="hero-content-section" data-section="3">
          <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold text-white leading-tight mb-6">
            Cultural
            <span class="bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent">
              Renaissance
            </span>
          </h1>
          <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
            Dive deep into the rich traditions, festivals, and artistic expressions that define Nigeria's cultural landscape.
          </p>
          <div class="mb-8">
            <p class="text-lg font-medium text-white mb-4">Celebrating our heritage, embracing our future.</p>
          </div>
        </div>

        <!-- Content Section 5: South Africa -->
        <div class="hero-content-section" data-section="4">
          <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold text-white leading-tight mb-6">
            The Rainbow
            <span class="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              Nation
            </span>
          </h1>
          <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
            Journey through South Africa's complex history, diverse cultures, and the ongoing story of transformation and hope.
          </p>
          <div class="mb-8">
            <p class="text-lg font-medium text-white mb-4">Unity in diversity, strength in truth.</p>
          </div>
        </div>

        <!-- Common CTA Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
          <a href="/watch" class="bg-gradient-to-r from-white/20 to-white/10 backdrop-blur-md text-white border border-white/30 px-8 py-4 rounded-lg hover:bg-white/30 transition-all font-medium text-lg">
            Watch Our Journey
          </a>
          <a href="/join" class="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-8 py-4 rounded-lg hover:from-orange-500 hover:to-red-500 transition-all font-medium text-lg">
            Join the Movement
          </a>
        </div>
      </div>

      <!-- Scroll Progress Indicator -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2">
        <div class="flex space-x-2">
          <div class="scroll-indicator active" data-indicator="0"></div>
          <div class="scroll-indicator" data-indicator="1"></div>
          <div class="scroll-indicator" data-indicator="2"></div>
          <div class="scroll-indicator" data-indicator="3"></div>
          <div class="scroll-indicator" data-indicator="4"></div>
        </div>
        <p class="text-white/70 text-sm mt-2 text-center">Scroll to explore</p>
      </div>
    </div>


    
  </div>

  <!-- Featured Video/Journey Preview -->
    <div class="relative max-w-6xl mx-auto">
      <div class="relative bg-white rounded-2xl shadow-2xl border border-gray-200 overflow-hidden">
        <!-- Video Header -->
        <div class="bg-gradient-to-r from-african-red/10 to-african-gold/10 border-b border-gray-200 px-6 py-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="w-3 h-3 bg-african-red rounded-full"></div>
              <div class="w-3 h-3 bg-african-gold rounded-full"></div>
              <div class="w-3 h-3 bg-african-green rounded-full"></div>
              <div class="text-sm font-medium text-gray-700 ml-4">Journey Through 32 Countries</div>
            </div>
            <span class="text-xs text-gray-500">36 Weeks • Documentary Series</span>
          </div>
        </div>

        <div class="p-8">
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Journey Stats -->
            <div class="space-y-6">
              <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-r from-african-red to-african-gold rounded-full flex items-center justify-center mx-auto mb-3">
                  <span class="text-white font-bold text-xl">32</span>
                </div>
                <h3 class="font-semibold text-gray-900">Countries Visited</h3>
                <p class="text-sm text-gray-600">Across all regions of Africa</p>
              </div>

              <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-r from-african-gold to-african-green rounded-full flex items-center justify-center mx-auto mb-3">
                  <span class="text-white font-bold text-xl">36</span>
                </div>
                <h3 class="font-semibold text-gray-900">Weeks of Journey</h3>
                <p class="text-sm text-gray-600">Documenting every step</p>
              </div>

              <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-r from-african-green to-african-red rounded-full flex items-center justify-center mx-auto mb-3">
                  <span class="text-white font-bold text-xl">∞</span>
                </div>
                <h3 class="font-semibold text-gray-900">Stories Shared</h3>
                <p class="text-sm text-gray-600">Voices from the continent</p>
              </div>
            </div>

            <!-- Featured Video -->
            <div class="lg:col-span-2">
              <div class="bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl aspect-video flex items-center justify-center relative overflow-hidden p-4 sm:p-6">
                <div class="absolute inset-0 bg-gradient-to-br from-african-red/20 to-african-gold/20"></div>
                <div class="relative text-center text-white">
                  <div class="w-16 h-16 sm:w-20 sm:h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4 backdrop-blur-sm">
                    <svg class="w-6 h-6 sm:w-8 sm:h-8 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                  <h3 class="text-sm md:text-xl font-bold mb-2">Watch the Journey</h3>
                  <p class="text-xs sm:text-sm text-white/80">Experience Africa through authentic stories</p>
                </div>
              </div>

              <div class="mt-6 grid grid-cols-2 gap-4">
                <div class="bg-gradient-to-r from-african-red/10 to-african-gold/10 rounded-lg p-4 text-center">
                  <h4 class="font-semibold text-gray-900 mb-1">Latest Episode</h4>
                  <p class="text-sm text-gray-600">Ghana: The Gateway to Africa</p>
                </div>
                <div class="bg-gradient-to-r from-african-gold/10 to-african-green/10 rounded-lg p-4 text-center">
                  <h4 class="font-semibold text-gray-900 mb-1">Weekly Podcast</h4>
                  <p class="text-sm text-gray-600">Voices from the Diaspora</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
</section>

<style>
  /* Dynamic Hero Styles */
  .dynamic-hero {
    height: 100vh;
    position: relative;
    overflow: hidden;
  }

  /* Background Image Container */
  .hero-bg-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
  }

  .hero-bg-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 1s ease-in-out, transform 0.5s ease-out;
    transform: scale(1.1);
  }

  .hero-bg-image.active {
    opacity: 1;
    transform: scale(1);
  }

  /* Content Sections */
  .hero-content-section {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease-in-out, transform 0.8s ease-in-out;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    pointer-events: none;
  }

  .hero-content-section.active {
    opacity: 1;
    transform: translateY(0);
    position: relative;
    pointer-events: auto;
  }

  /* Scroll Progress Indicators */
  .scroll-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .scroll-indicator.active {
    background-color: rgba(255, 255, 255, 0.9);
    transform: scale(1.2);
  }

  .scroll-indicator:hover {
    background-color: rgba(255, 255, 255, 0.7);
    transform: scale(1.1);
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .dynamic-hero {
      height: 100vh;
    }

    .hero-content-section h1 {
      font-size: 2.5rem;
      line-height: 1.2;
    }

    .hero-content-section p {
      font-size: 1rem;
    }
  }

  @media (max-width: 480px) {
    .hero-content-section h1 {
      font-size: 2rem;
    }

    .hero-content-section .flex {
      flex-direction: column;
      gap: 1rem;
    }
  }

  /* Parallax effect for background images */
  @media (prefers-reduced-motion: no-preference) {
    .hero-bg-image {
      transition: opacity 1s ease-in-out, transform 0.8s ease-out;
    }

    .hero-bg-image.active {
      animation: subtle-zoom 20s ease-in-out infinite alternate;
    }
  }

  @keyframes subtle-zoom {
    0% {
      transform: scale(1);
    }
    100% {
      transform: scale(1.05);
    }
  }

  /* Accessibility - Reduced Motion */
  @media (prefers-reduced-motion: reduce) {
    .hero-bg-image,
    .hero-content-section,
    .scroll-indicator {
      transition: none !important;
      animation: none !important;
    }
  }
</style>

<script>
  // Dynamic Hero Scroll Functionality
  document.addEventListener('DOMContentLoaded', () => {
    const heroSection = document.getElementById('dynamic-hero');
    const bgImages = document.querySelectorAll('.hero-bg-image');
    const contentSections = document.querySelectorAll('.hero-content-section');
    const indicators = document.querySelectorAll('.scroll-indicator');

    let currentSection = 0;
    let isScrolling = false;
    let scrollTimeout: ReturnType<typeof setTimeout>;

    // Configuration
    const totalSections = bgImages.length;
    const sectionHeight = window.innerHeight;

    // Initialize Intersection Observer for scroll detection
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: [0, 0.25, 0.5, 0.75, 1]
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const scrollProgress = entry.intersectionRatio;
          updateHeroContent(scrollProgress);
        }
      });
    }, observerOptions);

    // Observe the hero section
    if (heroSection) {
      observer.observe(heroSection);
    }

    // Update hero content based on scroll progress
    function updateHeroContent(progress) {
      // Calculate which section should be active based on scroll progress
      const sectionIndex = Math.min(
        Math.floor(progress * totalSections),
        totalSections - 1
      );

      if (sectionIndex !== currentSection) {
        switchToSection(sectionIndex);
      }
    }

    // Switch to specific section
    function switchToSection(index: number) {
      if (index < 0 || index >= totalSections || index === currentSection) return;

      // Update current section
      currentSection = index;

      // Update background images
      bgImages.forEach((img, i) => {
        img.classList.toggle('active', i === index);
      });

      // Update content sections
      contentSections.forEach((section, i) => {
        section.classList.toggle('active', i === index);
      });

      // Update indicators
      indicators.forEach((indicator, i) => {
        indicator.classList.toggle('active', i === index);
      });
    }

    // Handle scroll events for more precise control
    let ticking = false;

    function handleScroll() {
      if (!ticking && heroSection) {
        requestAnimationFrame(() => {
          const heroRect = heroSection.getBoundingClientRect();
          const viewportHeight = window.innerHeight;

          // Calculate scroll progress within the hero section
          let scrollProgress = 0;

          if (heroRect.top <= 0 && heroRect.bottom >= 0) {
            // Hero is in viewport
            const visibleHeight = Math.min(heroRect.bottom, viewportHeight) - Math.max(heroRect.top, 0);
            scrollProgress = Math.max(0, Math.min(1, (viewportHeight - heroRect.top) / viewportHeight));

            // Calculate section based on scroll progress
            const targetSection = Math.min(
              Math.floor(scrollProgress * totalSections),
              totalSections - 1
            );

            if (targetSection !== currentSection) {
              switchToSection(targetSection);
            }
          }

          ticking = false;
        });
        ticking = true;
      }
    }

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Handle indicator clicks
    indicators.forEach((indicator, index) => {
      indicator.addEventListener('click', () => {
        switchToSection(index);

        // Smooth scroll to appropriate position
        const targetScroll = (index / totalSections) * sectionHeight;
        window.scrollTo({
          top: targetScroll,
          behavior: 'smooth'
        });
      });
    });

    // Handle keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (heroSection && isElementInViewport(heroSection)) {
        switch(e.key) {
          case 'ArrowDown':
          case 'ArrowRight':
            e.preventDefault();
            if (currentSection < totalSections - 1) {
              switchToSection(currentSection + 1);
            }
            break;
          case 'ArrowUp':
          case 'ArrowLeft':
            e.preventDefault();
            if (currentSection > 0) {
              switchToSection(currentSection - 1);
            }
            break;
        }
      }
    });

    // Utility function to check if element is in viewport
    function isElementInViewport(el: Element) {
      const rect = el.getBoundingClientRect();
      return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
      );
    }

    // Handle window resize
    window.addEventListener('resize', () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        handleScroll();
      }, 100);
    });

    // Preload images for better performance
    function preloadImages() {
      bgImages.forEach(img => {
        const imageUrl = (img as HTMLElement).dataset.image;
        if (imageUrl) {
          const preloadImg = new Image();
          preloadImg.src = imageUrl;
        }
      });
    }

    // Initialize
    preloadImages();
    switchToSection(0);
  });
</script>
