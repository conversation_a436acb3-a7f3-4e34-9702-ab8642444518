---
import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
---

<Layout title="Watch - PJM Africa">
  <Header />
  <main class="pt-14 sm:pt-16">
    <!-- Hero Section -->
    <section class="py-20 bg-gradient-to-br from-orange-50 via-red-50 to-yellow-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center max-w-4xl mx-auto">
          <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Watch
          </h1>
          <p class="text-xl text-gray-600 mb-8">
            Experience Africa through authentic documentary content, interviews, and cultural insights from our 32-country journey.
          </p>
        </div>
      </div>
    </section>

    <!-- Featured Video -->
    <section class="py-12 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-gradient-to-r from-african-red/10 to-african-gold/10 rounded-2xl p-8 mb-16">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
              <div class="flex items-center space-x-2 mb-4">
                <span class="bg-african-red text-white text-xs font-medium px-2.5 py-0.5 rounded">Featured</span>
                <span class="text-sm text-gray-500">Latest Episode</span>
              </div>
              <h2 class="text-3xl font-bold text-gray-900 mb-4">
                Ghana: The Gateway to Africa
              </h2>
              <p class="text-gray-600 mb-6">
                Discover how Ghana became the gateway for African diaspora reconnection and the birthplace of Pan-African consciousness.
              </p>
              <div class="flex items-center space-x-4">
                <span class="text-sm text-gray-500">West Africa Series</span>
                <span class="text-sm text-gray-500">•</span>
                <span class="text-sm text-gray-500">Episode 1</span>
                <span class="text-sm text-gray-500">•</span>
                <span class="text-sm text-gray-500">24 min</span>
              </div>
            </div>
            <div class="aspect-video bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl flex items-center justify-center relative overflow-hidden">
              <div class="absolute inset-0 bg-gradient-to-br from-african-red/20 to-african-gold/20"></div>
              <div class="relative text-center text-white">
                <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 backdrop-blur-sm">
                  <svg class="w-8 h-8 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </div>
                <p class="text-white/80">Click to watch on YouTube</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Regional Content -->
    <section class="py-20 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Explore by Region
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Journey through Africa's five distinct regions, each with unique stories and perspectives.
          </p>
        </div>

        <!-- Region Filter Tabs -->
        <div class="flex flex-wrap justify-center gap-4 mb-12" id="region-filters">
          <button class="region-filter active px-6 py-3 rounded-lg font-medium transition-all" data-region="all">
            All Regions
          </button>
          <button class="region-filter px-6 py-3 rounded-lg font-medium transition-all" data-region="west">
            West Africa
          </button>
          <button class="region-filter px-6 py-3 rounded-lg font-medium transition-all" data-region="east">
            East Africa
          </button>
          <button class="region-filter px-6 py-3 rounded-lg font-medium transition-all" data-region="south">
            Southern Africa
          </button>
          <button class="region-filter px-6 py-3 rounded-lg font-medium transition-all" data-region="central">
            Central Africa
          </button>
          <button class="region-filter px-6 py-3 rounded-lg font-medium transition-all" data-region="north">
            North Africa
          </button>
        </div>

        <!-- Video Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="video-grid">
          <!-- West Africa Videos -->
          <div class="video-item bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-shadow" data-region="west">
            <div class="aspect-video bg-gradient-to-br from-gray-900 to-gray-800 flex items-center justify-center relative">
              <div class="absolute inset-0 bg-gradient-to-br from-african-red/20 to-african-gold/20"></div>
              <div class="relative text-white">
                <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto backdrop-blur-sm">
                  <svg class="w-6 h-6 text-white ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </div>
              </div>
            </div>
            <div class="p-6">
              <div class="flex items-center space-x-2 mb-2">
                <span class="bg-african-red text-white text-xs font-medium px-2 py-1 rounded">West Africa</span>
                <span class="text-xs text-gray-500">Episode 2</span>
              </div>
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Nigeria: The Giant of Africa</h3>
              <p class="text-gray-600 text-sm mb-4">Exploring Nigeria's cultural influence and economic power across the continent.</p>
              <div class="text-xs text-gray-500">18 min • Lagos, Nigeria</div>
            </div>
          </div>

          <!-- East Africa Videos -->
          <div class="video-item bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-shadow" data-region="east">
            <div class="aspect-video bg-gradient-to-br from-gray-900 to-gray-800 flex items-center justify-center relative">
              <div class="absolute inset-0 bg-gradient-to-br from-green-500/20 to-blue-500/20"></div>
              <div class="relative text-white">
                <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto backdrop-blur-sm">
                  <svg class="w-6 h-6 text-white ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </div>
              </div>
            </div>
            <div class="p-6">
              <div class="flex items-center space-x-2 mb-2">
                <span class="bg-green-600 text-white text-xs font-medium px-2 py-1 rounded">East Africa</span>
                <span class="text-xs text-gray-500">Episode 15</span>
              </div>
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Kenya: Wildlife and Innovation</h3>
              <p class="text-gray-600 text-sm mb-4">How Kenya balances conservation with technological advancement.</p>
              <div class="text-xs text-gray-500">22 min • Nairobi, Kenya</div>
            </div>
          </div>

          <!-- Southern Africa Videos -->
          <div class="video-item bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-shadow" data-region="south">
            <div class="aspect-video bg-gradient-to-br from-gray-900 to-gray-800 flex items-center justify-center relative">
              <div class="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-pink-500/20"></div>
              <div class="relative text-white">
                <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto backdrop-blur-sm">
                  <svg class="w-6 h-6 text-white ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </div>
              </div>
            </div>
            <div class="p-6">
              <div class="flex items-center space-x-2 mb-2">
                <span class="bg-purple-600 text-white text-xs font-medium px-2 py-1 rounded">Southern Africa</span>
                <span class="text-xs text-gray-500">Episode 28</span>
              </div>
              <h3 class="text-lg font-semibold text-gray-900 mb-2">South Africa: Rainbow Nation</h3>
              <p class="text-gray-600 text-sm mb-4">The journey from apartheid to democracy and ongoing transformation.</p>
              <div class="text-xs text-gray-500">26 min • Cape Town, South Africa</div>
            </div>
          </div>

          <!-- Central Africa Videos -->
          <div class="video-item bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-shadow" data-region="central">
            <div class="aspect-video bg-gradient-to-br from-gray-900 to-gray-800 flex items-center justify-center relative">
              <div class="absolute inset-0 bg-gradient-to-br from-green-600/20 to-yellow-500/20"></div>
              <div class="relative text-white">
                <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto backdrop-blur-sm">
                  <svg class="w-6 h-6 text-white ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </div>
              </div>
            </div>
            <div class="p-6">
              <div class="flex items-center space-x-2 mb-2">
                <span class="bg-green-700 text-white text-xs font-medium px-2 py-1 rounded">Central Africa</span>
                <span class="text-xs text-gray-500">Episode 35</span>
              </div>
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Cameroon: Cultural Crossroads</h3>
              <p class="text-gray-600 text-sm mb-4">Discovering the linguistic and cultural diversity of Central Africa.</p>
              <div class="text-xs text-gray-500">20 min • Yaoundé, Cameroon</div>
            </div>
          </div>

          <!-- North Africa Videos -->
          <div class="video-item bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-shadow" data-region="north">
            <div class="aspect-video bg-gradient-to-br from-gray-900 to-gray-800 flex items-center justify-center relative">
              <div class="absolute inset-0 bg-gradient-to-br from-yellow-500/20 to-orange-500/20"></div>
              <div class="relative text-white">
                <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto backdrop-blur-sm">
                  <svg class="w-6 h-6 text-white ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </div>
              </div>
            </div>
            <div class="p-6">
              <div class="flex items-center space-x-2 mb-2">
                <span class="bg-yellow-600 text-white text-xs font-medium px-2 py-1 rounded">North Africa</span>
                <span class="text-xs text-gray-500">Episode 42</span>
              </div>
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Egypt: Ancient Meets Modern</h3>
              <p class="text-gray-600 text-sm mb-4">How ancient Egyptian civilization influences modern African identity.</p>
              <div class="text-xs text-gray-500">25 min • Cairo, Egypt</div>
            </div>
          </div>

          <!-- More videos for each region... -->
          <div class="video-item bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-shadow" data-region="west">
            <div class="aspect-video bg-gradient-to-br from-gray-900 to-gray-800 flex items-center justify-center relative">
              <div class="absolute inset-0 bg-gradient-to-br from-african-red/20 to-african-gold/20"></div>
              <div class="relative text-white">
                <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto backdrop-blur-sm">
                  <svg class="w-6 h-6 text-white ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </div>
              </div>
            </div>
            <div class="p-6">
              <div class="flex items-center space-x-2 mb-2">
                <span class="bg-african-red text-white text-xs font-medium px-2 py-1 rounded">West Africa</span>
                <span class="text-xs text-gray-500">Episode 8</span>
              </div>
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Senegal: The Teranga Spirit</h3>
              <p class="text-gray-600 text-sm mb-4">Understanding Senegalese hospitality and its role in African unity.</p>
              <div class="text-xs text-gray-500">19 min • Dakar, Senegal</div>
            </div>
          </div>
        </div>

        <!-- Load More -->
        <div class="text-center mt-12">
          <button class="bg-gradient-to-r from-african-red to-african-gold text-white px-8 py-3 rounded-lg hover:from-african-gold hover:to-african-red transition-all font-medium">
            Load More Episodes
          </button>
        </div>
      </div>
    </section>

    <!-- YouTube Channel CTA -->
    <section class="py-20 bg-white">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
          Subscribe to Our YouTube Channel
        </h2>
        <p class="text-xl text-gray-600 mb-8">
          Get notified when new episodes are released and join our growing community of African storytellers.
        </p>
        <a href="https://youtube.com/@pjmafrica" target="_blank" class="inline-flex items-center bg-red-600 text-white px-8 py-4 rounded-lg hover:bg-red-700 transition-colors font-medium text-lg">
          <svg class="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 24 24">
            <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
          </svg>
          Subscribe on YouTube
        </a>
      </div>
    </section>
  </main>
  <Footer />
</Layout>

<script>
  // Region filtering functionality
  document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('.region-filter');
    const videoItems = document.querySelectorAll('.video-item');

    // Set initial active state
    updateActiveFilter('all');

    filterButtons.forEach(button => {
      button.addEventListener('click', function() {
        const region = this.dataset.region;
        
        // Update active filter
        updateActiveFilter(region);
        
        // Filter videos
        videoItems.forEach(item => {
          if (region === 'all' || item.dataset.region === region) {
            item.style.display = 'block';
          } else {
            item.style.display = 'none';
          }
        });
      });
    });

    function updateActiveFilter(activeRegion) {
      filterButtons.forEach(btn => {
        if (btn.dataset.region === activeRegion) {
          btn.classList.add('active', 'bg-gradient-to-r', 'from-african-red', 'to-african-gold', 'text-white');
          btn.classList.remove('bg-gray-100', 'text-gray-700', 'hover:bg-gray-200');
        } else {
          btn.classList.remove('active', 'bg-gradient-to-r', 'from-african-red', 'to-african-gold', 'text-white');
          btn.classList.add('bg-gray-100', 'text-gray-700', 'hover:bg-gray-200');
        }
      });
    }
  });
</script>
