# PJM Africa - Pan-African Media Platform

A comprehensive Pan-African media and cultural platform built with Astro.js and Tailwind CSS. Originally transformed from an AI platform clone, this project now serves as the digital home for PJM Africa's mission to connect Africans across the continent and diaspora through authentic storytelling.

## 🚀 Features

- **Pan-African Focus**: Content covering all 32 African countries and diaspora communities
- **Multi-Media Platform**: Documentary videos, podcasts, and written content
- **Interactive Journey Map**: Explore Africa through five distinct regions
- **Community Engagement**: Newsletter signup, donation, and partnership opportunities
- **Responsive Design**: Fully responsive layout that works on all devices
- **Modern Styling**: African-inspired color palette with Tailwind CSS
- **Performance Optimized**: Static site generation with Astro.js

## 🏗️ Platform Architecture

PJM Africa is built as a comprehensive Pan-African media platform with the following structure:

### Content Strategy
- **Documentary Content**: Video series covering 32 African countries
- **Podcast Platform**: Weekly episodes on politics, culture, identity, economics, and healing
- **Written Content**: Blog articles and cultural insights
- **Regional Organization**: Content sorted by West, East, Southern, Central, and North Africa

### Navigation Flow
- Fixed header with PJM Africa branding and African-inspired colors
- Mobile-responsive hamburger menu
- Content-focused navigation (Watch, Listen, Read, About, Advisory, Join)

### Component Architecture
- **AfricaJourney**: Interactive regional exploration component
- **Hero Section**: Mission-focused with call-to-action buttons
- **Statistics**: African-focused metrics (countries, episodes, community)
- **Newsletter Integration**: Community building and engagement

### Page Structure
- **Homepage**: Mission statement, journey preview, regional content
- **About**: Founder story, 32-country timeline, mission & vision
- **Watch**: YouTube content organized by African regions
- **Listen**: Podcast episodes with audio players and platform links
- **Read**: Blog articles on African topics
- **Advisory**: Professional services for governments and organizations
- **Join**: Newsletter, donations, partnerships, sponsorship opportunities

### Styling Approach
- **African Color Palette**: Red, gold, green inspired by Pan-African colors
- **Cultural Elements**: African-themed gradients and imagery
- **Responsive Design**: Mobile-first approach for global accessibility
- **Interactive Elements**: Region switching, audio players, form submissions

## 📁 Project Structure

```text
/
├── public/
│   └── favicon.svg
├── src/
│   ├── components/
│   │   ├── Header.astro          # PJM Africa branding & navigation
│   │   ├── Hero.astro            # Mission-focused hero section
│   │   ├── AfricaJourney.astro   # Interactive regional exploration
│   │   ├── StatsSection.astro    # African impact metrics
│   │   └── Footer.astro          # Community links & resources
│   ├── layouts/
│   │   └── Layout.astro          # Base layout with African theme
│   └── pages/
│       ├── index.astro           # Homepage with journey preview
│       ├── about.astro           # Founder story & mission
│       ├── watch.astro           # YouTube content by region
│       ├── listen.astro          # Podcast episodes & players
│       ├── blog.astro            # African-focused articles
│       ├── advisory.astro        # Professional services
│       ├── join.astro            # Community engagement
│       └── 404.astro             # Custom error page
├── astro.config.mjs
├── tailwind.config.mjs           # African color palette
└── package.json
```

## 🧞 Commands

All commands are run from the root of the project:

| Command                   | Action                                           |
| :------------------------ | :----------------------------------------------- |
| `npm install`             | Installs dependencies                            |
| `npm run dev`             | Starts local dev server at `localhost:4321`     |
| `npm run build`           | Build your production site to `./dist/`          |
| `npm run preview`         | Preview your build locally, before deploying     |
| `npm run astro ...`       | Run CLI commands like `astro add`, `astro check` |
| `npm run astro -- --help` | Get help using the Astro CLI                     |

## 🎨 Design System

### African Color Palette
- **Primary**: Red to Gold gradient (`from-african-red to-african-gold`)
- **Secondary**: Green, sunset orange, and earth tones
- **Cultural**: Pan-African inspired colors (red, gold, green)
- **Neutral**: Gray scale for text and backgrounds

### Typography
- **Font**: System fonts for optimal performance and accessibility
- **Hierarchy**: Clear heading structure emphasizing African identity
- **Responsive**: Mobile-first text sizing for global reach

### Key Components
- **Header**: PJM Africa branding with African-inspired navigation
- **Hero**: Mission-focused with journey preview and CTAs
- **AfricaJourney**: Interactive regional exploration with country details
- **Statistics**: African impact metrics with animated counters
- **Newsletter**: Community building and engagement forms
- **Footer**: Comprehensive links to content, regions, and services

## 🔧 Technical Implementation

### Astro.js Features Used
- Static Site Generation (SSG)
- Component-based architecture
- TypeScript support
- Tailwind CSS integration

### JavaScript Functionality
- Tab switching with DOM manipulation
- Animated counters with Intersection Observer
- Mobile menu toggle
- FAQ accordion interactions

### Performance Optimizations
- Static generation for fast loading
- Optimized images and assets
- Minimal JavaScript bundle
- CSS purging with Tailwind

## 🌐 Pages Implemented

1. **Homepage** (`/`) - Mission statement, journey preview, and regional exploration
2. **About** (`/about`) - Founder's story, 32-country timeline, mission & vision
3. **Watch** (`/watch`) - YouTube documentary content organized by African regions
4. **Listen** (`/listen`) - Podcast episodes with audio players and streaming links
5. **Read** (`/blog`) - African-focused articles on politics, culture, identity, economics
6. **Advisory** (`/advisory`) - Professional services for governments and organizations
7. **Join** (`/join`) - Newsletter signup, donations, partnerships, sponsorship
8. **404** (`/404`) - Custom error page with African branding

## 📱 Responsive Design

The platform is fully responsive and globally accessible:
- **Mobile-first**: Designed for African mobile-first internet usage
- **Cross-device**: Optimized for smartphones, tablets, and desktops
- **Touch-friendly**: Interactive elements designed for touch interfaces
- **Global accessibility**: Fast loading for various internet speeds

## 🚀 Getting Started

1. Clone the repository
2. Install dependencies: `npm install`
3. Start the development server: `npm run dev`
4. Open `http://localhost:4321` in your browser

## 🌍 Mission

PJM Africa connects Africans across the continent and diaspora through:
- **Truth**: Authentic storytelling and cultural insights
- **Identity**: Celebrating African heritage and diversity
- **Opportunity**: Building bridges for economic and social development

## 📄 License

This project represents the digital platform for PJM Africa's mission to unite Africans through storytelling and cultural exchange.
