<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slide Menu Design</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* Custom CSS for transitions and specific element styling not easily done with Tailwind */
        body {
            font-family: "Inter", sans-serif;
            margin: 0;
            overflow: hidden; /* Prevent body scroll when menu is open */
        }

        /* Container for the entire application, handles overall transformation */
        .app-container {
            position: relative;
            height: 100vh;
            display: flex;
            background-color: #e5e7eb; /* Light gray background, visible when main screen shrinks */
            transition: transform 0.5s ease-out; /* Smooth transition for entire app */
            overflow: hidden; /* Ensures elements don't spill out during transition */
        }

        /* Main screen that shrinks and gets rounded corners */
        .main-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: white;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border-radius: 0; /* Initial state, no border-radius */
            transition: transform 0.5s ease-out, border-radius 0.5s ease-out;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            box-sizing: border-box;
            z-index: 10; /* Ensure it's above the slide menu */
        }

        /* Overlay for the main screen when menu is open */
        .main-screen::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0); /* Initially transparent */
            transition: background-color 0.5s ease-out;
            pointer-events: none; /* Allows clicks to pass through initially */
        }

        /* Slide menu itself, initially off-screen */
        .slide-menu {
            position: absolute;
            top: 0;
            left: 0;
            width: 256px; /* Fixed width for the menu (w-64 in Tailwind) */
            height: 100%;
            background-color: #1f2937; /* Dark gray background */
            color: white;
            transform: translateX(-100%); /* Initially completely off-screen to the left */
            transition: transform 0.5s ease-out;
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            z-index: 5; /* Below the main screen initially */
        }

        /* Styles for individual menu items within the slide menu */
        .menu-item {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            opacity: 0; /* Initially hidden for staggered animation */
            transform: translateX(-20px); /* Slightly off-set for animation */
            transition: opacity 0.3s ease-out, transform 0.3s ease-out;
            cursor: pointer;
            width: 100%;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
        }

        .menu-item:hover {
            background-color: #374151; /* Darker gray on hover */
        }

        .menu-item .icon {
            font-size: 1.25rem;
            margin-right: 1rem;
            width: 24px; /* Fixed width for icon to ensure alignment */
            text-align: center;
        }

        .menu-item span {
            font-size: 1.125rem;
        }

        /* Close button inside the slide menu */
        .close-button {
            background: none;
            border: none;
            color: white;
            font-size: 2rem;
            cursor: pointer;
            align-self: flex-end; /* Align to top right of menu */
            margin-bottom: 2rem;
            transition: transform 0.3s ease-in-out;
            opacity: 0; /* Initially hidden */
            transform: translateX(20px); /* Slightly off-set */
        }

        .close-button:hover {
            transform: scale(1.1);
        }

        /* Menu toggle button on the main screen */
        .menu-toggle-button {
            position: absolute;
            top: 1.5rem;
            left: 1.5rem;
            background: none;
            border: none;
            font-size: 2rem;
            color: #333;
            cursor: pointer;
            z-index: 20; /* Ensure it's above the overlay */
        }

        .menu-toggle-button:hover {
            transform: scale(1.05);
        }

        /* State when menu is open */
        body.menu-open .main-screen {
            transform: scale(0.85) translateX(calc(256px / 2)); /* Scale down and shift right by half the menu width */
            border-radius: 1.5rem; /* Rounded corners */
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        body.menu-open .main-screen::before {
            background-color: rgba(0, 0, 0, 0.3); /* Darken overlay */
            pointer-events: auto; /* Allow clicks on overlay to close menu */
            cursor: pointer;
        }

        body.menu-open .slide-menu {
            transform: translateX(0); /* Slide into view */
        }

        /* Animate menu items and close button when menu is open */
        body.menu-open .menu-item {
            opacity: 1;
            transform: translateX(0);
        }

        /* Staggered animation for menu items */
        body.menu-open .menu-item:nth-child(1) { transition-delay: 0.1s; }
        body.menu-open .menu-item:nth-child(2) { transition-delay: 0.15s; }
        body.menu-open .menu-item:nth-child(3) { transition-delay: 0.2s; }
        body.menu-open .menu-item:nth-child(4) { transition-delay: 0.25s; }
        body.menu-open .menu-item:nth-child(5) { transition-delay: 0.3s; }
        body.menu-open .menu-item:nth-child(6) { transition-delay: 0.35s; }

        body.menu-open .close-button {
            opacity: 1;
            transform: translateX(0);
            transition-delay: 0.1s;
        }

        /* Adjustments for smaller screens */
        @media (max-width: 768px) {
            .slide-menu {
                width: 70%; /* Make menu wider on small screens */
            }

            body.menu-open .main-screen {
                transform: scale(0.9) translateX(calc(70vw / 2)); /* Adjust translation for wider menu */
            }
        }

        @media (max-width: 480px) {
            .slide-menu {
                width: 85%; /* Even wider on very small screens */
            }
            body.menu-open .main-screen {
                transform: scale(0.9) translateX(calc(85vw / 2));
            }
            .menu-toggle-button, .close-button {
                font-size: 1.75rem;
            }
            .menu-item .icon {
                font-size: 1.1rem;
            }
            .menu-item span {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body class="bg-gray-100">

    <div id="appContainer" class="app-container">
        <!-- The Main Screen Content -->
        <div id="mainScreen" class="main-screen">
            <button id="menuIcon" class="menu-toggle-button" aria-label="Open menu">
                <i class="fas fa-bars"></i>
            </button>

            <div class="text-center p-8">
                <h1 class="text-4xl font-extrabold text-gray-900 mb-4">Welcome to the App!</h1>
                <p class="text-lg text-gray-700 leading-relaxed max-w-xl mx-auto">
                    This is the main content area of your application. Experience a fluid and interactive slide menu by clicking the icon on the top left.
                    Designed with modern aesthetics and smooth transitions for an intuitive user experience.
                </p>
                <div class="mt-8 flex flex-wrap justify-center gap-4">
                    <a href="#" class="px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg shadow-md hover:bg-blue-700 transition-colors duration-300">Get Started</a>
                    <a href="#" class="px-6 py-3 bg-gray-200 text-gray-800 font-semibold rounded-lg shadow-md hover:bg-gray-300 transition-colors duration-300">Learn More</a>
                </div>
            </div>
        </div>

        <!-- The Slide Menu -->
        <div id="slideMenu" class="slide-menu">
            <button id="closeIcon" class="close-button" aria-label="Close menu">
                <i class="fas fa-times"></i>
            </button>

            <div class="menu-item-group w-full flex flex-col items-start pt-4">
                <div class="menu-item" data-name="Home">
                    <span class="icon"><i class="fas fa-home"></i></span>
                    <span>Home</span>
                </div>
                <div class="menu-item" data-name="Profile">
                    <span class="icon"><i class="fas fa-user-circle"></i></span>
                    <span>Profile</span>
                </div>
                <div class="menu-item" data-name="Settings">
                    <span class="icon"><i class="fas fa-cog"></i></span>
                    <span>Settings</span>
                </div>
                <div class="menu-item" data-name="Messages">
                    <span class="icon"><i class="fas fa-envelope"></i></span>
                    <span>Messages</span>
                </div>
                <div class="menu-item" data-name="Notifications">
                    <span class="icon"><i class="fas fa-bell"></i></span>
                    <span>Notifications</span>
                </div>
                <div class="menu-item" data-name="Logout">
                    <span class="icon"><i class="fas fa-sign-out-alt"></i></span>
                    <span>Logout</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const body = document.body;
            const menuIcon = document.getElementById('menuIcon');
            const closeIcon = document.getElementById('closeIcon');
            const mainScreen = document.getElementById('mainScreen'); // Used for overlay click

            // Function to open the menu
            function openMenu() {
                body.classList.add('menu-open');
            }

            // Function to close the menu
            function closeMenu() {
                body.classList.remove('menu-open');
            }

            // Event listener for opening the menu
            menuIcon.addEventListener('click', openMenu);

            // Event listener for closing the menu using the 'X' icon
            closeIcon.addEventListener('click', closeMenu);

            // Event listener for closing the menu by clicking on the dimmed main screen overlay
            // We attach this to mainScreen, and the ::before pseudo-element handles the click area when active
            mainScreen.addEventListener('click', (event) => {
                // Check if the click happened on the overlay (which is the mainScreen element itself when active)
                // This is important to not close the menu if a user clicks on content within the main screen
                // when the menu is NOT open.
                if (body.classList.contains('menu-open') && event.target === mainScreen) {
                    closeMenu();
                }
            });

            // Optional: Log which menu item was clicked (for demonstration)
            document.querySelectorAll('.menu-item').forEach(item => {
                item.addEventListener('click', () => {
                    const itemName = item.dataset.name;
                    console.log(`Clicked on: ${itemName}`);
                    // In a real app, you would navigate or perform an action here
                    closeMenu(); // Close menu after selection
                });
            });
        });
    </script>
</body>
</html>
